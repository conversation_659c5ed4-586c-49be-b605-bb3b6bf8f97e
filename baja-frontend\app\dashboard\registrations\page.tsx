'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { pendaftaranService, PendaftaranEvent } from '@/services/pendaftaranService';
import { eventService, Event } from '@/services/eventService';

export default function RegistrationsPage() {
  const router = useRouter();
  const [registrations, setRegistrations] = useState<PendaftaranEvent[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedRegistration, setSelectedRegistration] = useState<PendaftaranEvent | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<number | null>(null);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [search, setSearch] = useState('');

  useEffect(() => {
    fetchRegistrations();
    fetchEvents();
  }, [currentPage, search]);

  const fetchRegistrations = async () => {
    try {
      setLoading(true);
      const data = await pendaftaranService.getAllPendaftaran(currentPage, 10, search);
      setRegistrations(data.pendaftaran);
      setTotalPages(data.pagination.totalPages);
      setTotalItems(data.pagination.total);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchEvents = async () => {
    try {
      const data = await eventService.getAllEvents(1, 100); // Get all events
      setEvents(data.events);
    } catch (error: any) {
      console.error('Failed to fetch events:', error.message);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchRegistrations();
  };

  const handleRegister = async () => {
    if (!selectedEventId) {
      toast.error('Please select an event');
      return;
    }

    try {
      await pendaftaranService.createPendaftaran({ id_event: selectedEventId });
      toast.success('Successfully registered for event!');
      setShowRegisterModal(false);
      setSelectedEventId(null);
      fetchRegistrations();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleDelete = (registration: PendaftaranEvent) => {
    setSelectedRegistration(registration);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedRegistration) return;

    setDeleteLoading(true);
    try {
      await pendaftaranService.deletePendaftaran(selectedRegistration.id);
      toast.success('Registration cancelled successfully!');
      setShowDeleteModal(false);
      setSelectedRegistration(null);
      fetchRegistrations();
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setDeleteLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading registrations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-yellow-400 mb-2">Event Registrations</h1>
            <p className="text-gray-300">Manage your event registrations</p>
          </div>
          <button
            onClick={() => setShowRegisterModal(true)}
            className="px-4 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors"
          >
            Register for Event
          </button>
        </div>

        {/* Search */}
        <div className="mb-6">
          <form onSubmit={handleSearch} className="flex gap-4">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search registrations..."
              className="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
            />
            <button
              type="submit"
              className="px-6 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors"
            >
              Search
            </button>
          </form>
        </div>

        {/* Registrations List */}
        <div className="bg-gray-900 rounded-lg overflow-hidden border border-yellow-400">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-yellow-600 text-black">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Event</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Kontingen</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Event Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Registration Fee</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Registered</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {registrations.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-8 text-center text-gray-400">
                      No registrations found
                    </td>
                  </tr>
                ) : (
                  registrations.map((registration) => (
                    <tr key={registration.id} className="hover:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">
                          {registration.pendaftaranEvent?.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {registration.pendaftaranKontingen?.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {registration.pendaftaranEvent?.start_date && (
                          <div>
                            <div>{formatDate(registration.pendaftaranEvent.start_date)}</div>
                            {registration.pendaftaranEvent.end_date && (
                              <div className="text-xs text-gray-400">
                                to {formatDate(registration.pendaftaranEvent.end_date)}
                              </div>
                            )}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {registration.pendaftaranEvent?.biaya_registrasi 
                          ? formatCurrency(registration.pendaftaranEvent.biaya_registrasi)
                          : 'Free'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {formatDate(registration.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link
                            href={`/dashboard/registrations/${registration.id}`}
                            className="text-yellow-400 hover:text-yellow-300"
                          >
                            View
                          </Link>
                          <button
                            onClick={() => handleDelete(registration)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Cancel
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-300">
              Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalItems)} of {totalItems} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Previous
              </button>
              <span className="px-3 py-1 bg-yellow-600 text-black rounded">
                {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Register Modal */}
      {showRegisterModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 p-6 rounded-lg border border-yellow-400 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Register for Event</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-yellow-400 mb-2">
                Select Event
              </label>
              <select
                value={selectedEventId || ''}
                onChange={(e) => setSelectedEventId(Number(e.target.value))}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                <option value="">Choose an event</option>
                {events.map((event) => (
                  <option key={event.id} value={event.id}>
                    {event.name} - {formatDate(event.start_date)}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex gap-4">
              <button
                onClick={() => setShowRegisterModal(false)}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleRegister}
                className="flex-1 px-4 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors"
              >
                Register
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 p-6 rounded-lg border border-yellow-400 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Confirm Cancellation</h3>
            <p className="text-gray-300 mb-6">
              Are you sure you want to cancel registration for "{selectedRegistration?.pendaftaranEvent?.name}"? This action cannot be undone.
            </p>
            <div className="flex gap-4">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                disabled={deleteLoading}
              >
                Keep Registration
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={deleteLoading}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {deleteLoading ? 'Cancelling...' : 'Cancel Registration'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
