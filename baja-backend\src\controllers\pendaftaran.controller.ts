import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import PendaftaranEvent from '../models/PendaftaranEvent';
import Event from '../models/Event';
import Kontingen from '../models/Kontingen';
import { Op } from 'sequelize';

export const getAllPendaftaran = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '', event_id = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const user = req.user;

    const whereClause: any = {};
    
    // Filter berdasarkan role
    if (user.role === 'admin-event') {
      // Admin event hanya melihat pendaftaran untuk event mereka
      const userEvents = await Event.findAll({ 
        where: { id_user: user.id },
        attributes: ['id']
      });
      const eventIds = userEvents.map(event => event.id);
      whereClause.id_event = { [Op.in]: eventIds };
    } else if (user.role === 'ketua-kontingen') {
      // Ketua kontingen hanya melihat pendaftaran kontingen mereka
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found. Please register your kontingen first.'
        });
        return;
      }
    }

    if (event_id) {
      whereClause.id_event = event_id;
    }

    // Status filtering removed as PendaftaranEvent model doesn't have status field
    // if (status) {
    //   whereClause.status = status;
    // }

    if (search) {
      // Search in related models
      const { count, rows } = await PendaftaranEvent.findAndCountAll({
        where: whereClause,
        limit: Number(limit),
        offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            model: Event,
            as: 'pendaftaranEvent',
            attributes: ['id', 'name', 'description', 'start_date', 'end_date', 'lokasi', 'biaya_registrasi', 'metode_pembayaran', 'status'],
            where: search ? {
              name: { [Op.like]: `%${search}%` }
            } : undefined
          },
          {
            model: Kontingen,
            as: 'pendaftaranKontingen',
            attributes: ['id', 'name'],
            where: search ? {
              name: { [Op.like]: `%${search}%` }
            } : undefined
          }
        ]
      });

      res.json({
        success: true,
        message: 'Event registrations retrieved successfully',
        data: {
          pendaftaran: rows,
          pagination: {
            total: count,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      });
      return;
    }

    const { count, rows } = await PendaftaranEvent.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Event,
          as: 'pendaftaranEvent',
          attributes: ['id', 'name', 'description', 'start_date', 'end_date', 'lokasi', 'biaya_registrasi', 'metode_pembayaran', 'status']
        },
        {
          model: Kontingen,
          as: 'pendaftaranKontingen',
          attributes: ['id', 'name']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Event registrations retrieved successfully',
      data: {
        pendaftaran: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get all pendaftaran error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getPendaftaranById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Filter berdasarkan role
    if (user.role === 'admin-event') {
      const userEvents = await Event.findAll({ 
        where: { id_user: user.id },
        attributes: ['id']
      });
      const eventIds = userEvents.map(event => event.id);
      whereClause.id_event = { [Op.in]: eventIds };
    } else if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const pendaftaran = await PendaftaranEvent.findOne({
      where: whereClause,
      include: [
        {
          model: Event,
          as: 'pendaftaranEvent',
          attributes: ['id', 'name', 'description', 'start_date', 'end_date', 'lokasi', 'biaya_registrasi', 'metode_pembayaran', 'status']
        },
        {
          model: Kontingen,
          as: 'pendaftaranKontingen',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!pendaftaran) {
      res.status(404).json({
        success: false,
        message: 'Event registration not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Event registration retrieved successfully',
      data: pendaftaran
    });
  } catch (error) {
    console.error('Get pendaftaran by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createPendaftaran = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const { id_event } = req.body;

    // Hanya ketua kontingen yang bisa mendaftar event
    if (user.role !== 'ketua-kontingen') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only ketua-kontingen can register for events.'
      });
      return;
    }

    // Get kontingen
    const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
    if (!kontingen) {
      res.status(404).json({
        success: false,
        message: 'Kontingen not found. Please register your kontingen first.'
      });
      return;
    }

    // Check if event exists
    const event = await Event.findByPk(id_event);
    if (!event) {
      res.status(404).json({
        success: false,
        message: 'Event not found'
      });
      return;
    }

    // Check if already registered
    const existingPendaftaran = await PendaftaranEvent.findOne({
      where: {
        id_event,
        id_kontingen: kontingen.id
      }
    });

    if (existingPendaftaran) {
      res.status(400).json({
        success: false,
        message: 'Your kontingen is already registered for this event'
      });
      return;
    }

    const pendaftaran = await PendaftaranEvent.create({
      id_event,
      id_kontingen: kontingen.id
    });

    res.status(201).json({
      success: true,
      message: 'Event registration created successfully',
      data: pendaftaran
    });
  } catch (error) {
    console.error('Create pendaftaran error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateStatusPendaftaran = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const user = req.user;

    // Hanya admin dan admin-event yang bisa update status
    if (user.role === 'ketua-kontingen') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin and admin-event can update registration status.'
      });
      return;
    }

    const whereClause: any = { id };
    
    // Admin-event hanya bisa update pendaftaran untuk event mereka
    if (user.role === 'admin-event') {
      const userEvents = await Event.findAll({ 
        where: { id_user: user.id },
        attributes: ['id']
      });
      const eventIds = userEvents.map(event => event.id);
      whereClause.id_event = { [Op.in]: eventIds };
    }

    const pendaftaran = await PendaftaranEvent.findOne({ where: whereClause });
    if (!pendaftaran) {
      res.status(404).json({
        success: false,
        message: 'Event registration not found'
      });
      return;
    }

    // Status field removed from PendaftaranEvent model
    // await pendaftaran.update({ status });

    res.json({
      success: true,
      message: `Registration updated successfully`,
      data: { id: pendaftaran.id }
    });
  } catch (error) {
    console.error('Update status pendaftaran error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deletePendaftaran = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Filter berdasarkan role
    if (user.role === 'admin-event') {
      const userEvents = await Event.findAll({ 
        where: { id_user: user.id },
        attributes: ['id']
      });
      const eventIds = userEvents.map(event => event.id);
      whereClause.id_event = { [Op.in]: eventIds };
    } else if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const pendaftaran = await PendaftaranEvent.findOne({ where: whereClause });
    if (!pendaftaran) {
      res.status(404).json({
        success: false,
        message: 'Event registration not found'
      });
      return;
    }

    await pendaftaran.destroy();

    res.json({
      success: true,
      message: 'Event registration deleted successfully'
    });
  } catch (error) {
    console.error('Delete pendaftaran error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
