'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { atletService, Atlet } from '@/services/atletService';

export default function AthletesPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [athletes, setAthletes] = useState<Atlet[]>([]);
  const [loading, setLoading] = useState(true);
  const [verifyingId, setVerifyingId] = useState<number | null>(null);
  const [selectedStatus, setSelectedStatus] = useState('');
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [search, setSearch] = useState('');

  useEffect(() => {
    fetchAthletes();
  }, [currentPage, search]);

  const fetchAthletes = async () => {
    try {
      setLoading(true);
      const data = await atletService.getAllAtlet(currentPage, 10, search);
      setAthletes(data.atlet);
      setTotalPages(data.pagination.totalPages);
      setTotalItems(data.pagination.total);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchAthletes();
  };

  const handleVerifyAthlete = async (athleteId: number, status: 'verified' | 'rejected') => {
    setVerifyingId(athleteId);
    try {
      await atletService.verifyAtlet(athleteId, status);
      toast.success(`Athlete ${status} successfully!`);
      fetchAthletes(); // Refresh the list
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setVerifyingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-600', text: 'text-black', label: 'Pending' },
      verified: { bg: 'bg-green-600', text: 'text-white', label: 'Verified' },
      rejected: { bg: 'bg-red-600', text: 'text-white', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  // Filter athletes based on selected status
  const filteredAthletes = athletes.filter(athlete => {
    if (selectedStatus && athlete.status_verifikasi !== selectedStatus) {
      return false;
    }
    return true;
  });

  // Check if user has permission
  if (user?.role !== 'admin-event' && user?.role !== 'ketua-kontingen') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading athletes...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Athletes Management</h1>
            <p className="text-gray-400 mt-1">
              {user?.role === 'admin-event' ? 'Verify athlete registrations' : 'Manage your athletes'}
            </p>
          </div>
          {user?.role === 'ketua-kontingen' && (
            <Link
              href="/dashboard/athletes/create"
              className="px-4 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors"
            >
              Add New Athlete
            </Link>
          )}
        </div>

        {/* Search and Filters */}
        <div className="bg-gray-900 p-6 rounded-lg border border-gray-700">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search athletes by name or NIK..."
              className="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
            />
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full md:w-48 px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="verified">Verified</option>
              <option value="rejected">Rejected</option>
            </select>
            <button
              type="submit"
              className="px-6 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors"
            >
              Search
            </button>
          </form>
        </div>

        {/* Athletes List */}
        <div className="bg-gray-900 rounded-lg overflow-hidden border border-yellow-400">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-yellow-600 text-black">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">NIK</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Gender</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Age</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Kontingen</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredAthletes.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center text-gray-400">
                      No athletes found
                    </td>
                  </tr>
                ) : (
                  filteredAthletes.map((athlete) => (
                    <tr key={athlete.id} className="hover:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {athlete.foto && (
                            <img
                              className="h-10 w-10 rounded-full mr-3"
                              src={athlete.foto}
                              alt={athlete.name}
                            />
                          )}
                          <div>
                            <div className="text-sm font-medium text-white">{athlete.name}</div>
                            <div className="text-sm text-gray-400">{formatDate(athlete.tanggal_lahir)}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {athlete.nik}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {athlete.jenis_kelamin === 'M' ? 'Male' : 'Female'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {athlete.umur || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {athlete.atletKontingen?.name || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(athlete.status_verifikasi)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link
                            href={`/dashboard/athletes/${athlete.id}`}
                            className="text-yellow-400 hover:text-yellow-300"
                          >
                            View
                          </Link>
                          {user?.role === 'ketua-kontingen' && (
                            <>
                              <Link
                                href={`/dashboard/athletes/${athlete.id}/edit`}
                                className="text-blue-400 hover:text-blue-300"
                              >
                                Edit
                              </Link>
                            </>
                          )}
                          {user?.role === 'admin-event' && athlete.status_verifikasi === 'pending' && (
                            <>
                              <button
                                onClick={() => handleVerifyAthlete(athlete.id, 'verified')}
                                disabled={verifyingId === athlete.id}
                                className="text-green-400 hover:text-green-300 disabled:opacity-50"
                              >
                                {verifyingId === athlete.id ? 'Verifying...' : 'Verify'}
                              </button>
                              <button
                                onClick={() => handleVerifyAthlete(athlete.id, 'rejected')}
                                disabled={verifyingId === athlete.id}
                                className="text-red-400 hover:text-red-300 disabled:opacity-50"
                              >
                                {verifyingId === athlete.id ? 'Rejecting...' : 'Reject'}
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-300">
              Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalItems)} of {totalItems} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Previous
              </button>
              <span className="px-3 py-1 bg-yellow-600 text-black rounded">
                {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </DashboardLayout>
    );
  }
}
